PODS:
  - facebook_app_events (0.0.1):
    - FBAudienceNetwork (= 6.16)
    - FBSDKCoreKit (~> 18.0)
    - Flutter
  - FBAEMKit (18.0.0):
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBAudienceNetwork (6.16.0)
  - FBSDKCoreKit (18.0.0):
    - FBAEMKit (= 18.0.0)
    - FBSDKCoreKit_Basics (= 18.0.0)
  - FBSDKCoreKit_Basics (18.0.0)
  - Flutter (1.0.0)
  - flutter_compass (0.0.1):
    - Flutter
  - Google-Mobile-Ads-SDK (11.10.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (5.2.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 11.10.0)
    - webview_flutter_wkwebview
  - GoogleUserMessagingPlatform (3.0.0)
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - facebook_app_events (from `.symlinks/plugins/facebook_app_events/ios`)
  - Flutter (from `Flutter`)
  - flutter_compass (from `.symlinks/plugins/flutter_compass/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - FBAEMKit
    - FBAudienceNetwork
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - Google-Mobile-Ads-SDK
    - GoogleUserMessagingPlatform

EXTERNAL SOURCES:
  facebook_app_events:
    :path: ".symlinks/plugins/facebook_app_events/ios"
  Flutter:
    :path: Flutter
  flutter_compass:
    :path: ".symlinks/plugins/flutter_compass/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  facebook_app_events: 015d0181fcb1bf667ada2b8f02d0f53b9ea296dc
  FBAEMKit: e34530df538b8eb8aeb53c35867715ba6c63ef0c
  FBAudienceNetwork: d1670939884e3a2e0ad98dca98d7e0c841417228
  FBSDKCoreKit: d3f479a69127acebb1c6aad91c1a33907bcf6c2f
  FBSDKCoreKit_Basics: 017b6dc2a1862024815a8229e75661e627ac1e29
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_compass: b236ab69b61545cce89fd58527f401a7587d5cc1
  Google-Mobile-Ads-SDK: 13e6e98edfd78ad8d8a791edb927658cc260a56f
  google_mobile_ads: dc2b2a5884bef7ab2b4ff30022a513df5373e208
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
