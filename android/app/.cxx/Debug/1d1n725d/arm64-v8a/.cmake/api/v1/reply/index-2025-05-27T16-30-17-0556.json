{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake", "cpack": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cpack", "ctest": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ctest", "root": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-28746e4e5ff5b04c0d1a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-ac70b7c7b7cc9e4cb142.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-374b335262a1d589e559.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-ac70b7c7b7cc9e4cb142.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-374b335262a1d589e559.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-28746e4e5ff5b04c0d1a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}