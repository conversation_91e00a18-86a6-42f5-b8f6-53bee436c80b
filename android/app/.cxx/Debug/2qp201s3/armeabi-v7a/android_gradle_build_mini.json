{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.27.4/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/app_dev/compass_vi/android/app/.cxx/Debug/2qp201s3/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/app_dev/compass_vi/android/app/.cxx/Debug/2qp201s3/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}