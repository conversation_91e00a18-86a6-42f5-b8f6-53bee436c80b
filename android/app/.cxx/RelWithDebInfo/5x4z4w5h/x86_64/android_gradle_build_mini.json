{"buildFiles": ["/opt/homebrew/Caskroom/flutter/3.27.4/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/app_dev/compass_vi/android/app/.cxx/RelWithDebInfo/5x4z4w5h/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/app_dev/compass_vi/android/app/.cxx/RelWithDebInfo/5x4z4w5h/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}