import 'dart:math' as math;
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_compass/flutter_compass.dart';

class CompassWidget extends StatefulWidget {
  final String compassImagePath;

  const CompassWidget({
    super.key,
    required this.compassImagePath,
  });

  @override
  State<CompassWidget> createState() => _CompassWidgetState();
}

class _CompassWidgetState extends State<CompassWidget> {
  double _filteredAngle = 0; // Góc đã làm mượt
  late double _alpha; // <PERSON><PERSON> số làm mượt (dynamic based on device)

  @override
  void initState() {
    super.initState();
    // Điều chỉnh alpha cho iPhone 14 Pro Max iOS 18.4.1
    _alpha = _getAlphaForDevice();
  }

  /// Lấy alpha phù hợp cho từng device
  double _getAlphaForDevice() {
    if (Platform.isIOS) {
      // iPhone 14 Pro Max cần alpha thấp hơn để mượt hơn
      return 0.15; // Thay vì 0.33
    }
    return 0.33; // Default cho Android và các device khác
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<CompassEvent>(
      stream: FlutterCompass.events,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return const Center(child: Text('Lỗi khi đọc dữ liệu cảm biến'));
        }

        final CompassEvent? event = snapshot.data;
        final double? direction = event?.heading;

        if (direction == null) {
          return const Center(child: Text('Không có dữ liệu cảm biến'));
        }

        // Chuyển góc âm sang dương
        double newAngle = direction >= 0 ? direction : direction + 360;

        // Fix cho iPhone 14 Pro Max: Kiểm tra nếu stuck ở 359°
        if (Platform.isIOS && newAngle >= 358.0 && newAngle <= 360.0) {
          // Force reset nếu stuck ở 359° quá lâu
          if (_filteredAngle >= 358.0 && _filteredAngle <= 360.0) {
            newAngle = 0.0; // Reset về 0°
          }
        }

        // Tính delta góc (giữa góc mới và góc làm mượt hiện tại)
        double deltaAngle = newAngle - _filteredAngle;

        // Đảm bảo deltaAngle xoay theo hướng ngắn nhất
        if (deltaAngle > 180) {
          deltaAngle -= 360;
        } else if (deltaAngle < -180) {
          deltaAngle += 360;
        }

        // Cập nhật góc đã làm mượt với alpha động
        _filteredAngle += _alpha * deltaAngle;

        // Giới hạn _filteredAngle trong phạm vi 0–360 để kiểm soát giá trị
        if (_filteredAngle == 0) {
          // Giữ nguyên giá trị 0, không làm gì
        } else if (_filteredAngle >= 360) {
          _filteredAngle -= 360;
        } else if (_filteredAngle < 0) {
          _filteredAngle += 360;
        }

        return Transform.rotate(
          angle: (_filteredAngle * math.pi / 180) *
              -1, // Đổi sang radian và xoay theo chiều âm
          child: Image.asset(
            widget.compassImagePath,
            height: 400,
            width: 400,
            fit: BoxFit.contain,
          ),
        );
      },
    );
  }
}
